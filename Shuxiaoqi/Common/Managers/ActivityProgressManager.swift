import Foundation
import UIKit

/// 活跃度进度管理器
/// 负责管理用户活跃度的上报和状态跟踪
class ActivityProgressManager {
    
    // MARK: - 单例
    static let shared = ActivityProgressManager()
    private init() {
        setupNotifications()
    }
    
    // MARK: - 属性

    /// 是否正在跟踪活跃度
    private var isTracking: Bool = false

    /// 是否已经初始化过（只有第一次进入任务中心时才启动）
    private var hasInitialized: Bool = false

    /// 活跃度开始时间（用于实时计算总活跃时长）
    private var activityStartTime: Date?

    /// 服务器返回的基础活跃时长（秒）
    private var baseActiveSeconds: Int = 0

    /// 上次上报时间
    private var lastReportTime: Date?

    /// 累计活跃时长（秒）
    private var accumulatedSeconds: Int = 0

    /// 上报间隔（秒）- 每30秒上报一次
    private let reportInterval: TimeInterval = 30

    /// 定时器
    private var reportTimer: Timer?

    /// 当前活跃度数据
    private var currentActiveData: ActiveProgressData?

    // MARK: - 宝箱倒计时相关属性

    /// 宝箱倒计时开始时间
    private var treasureBoxStartTime: Date?

    /// 当前宝箱倒计时目标时长（秒）
    private var treasureBoxTargetDuration: Int = 0

    /// 是否正在进行宝箱倒计时
    private var isTreasureBoxCountingDown: Bool = false

    /// 宝箱倒计时定期检查定时器（每10秒检查一次）
    private var treasureBoxReportTimer: Timer?

    /// 宝箱倒计时检查间隔（秒）
    private let treasureBoxReportInterval: TimeInterval = 10
    
    // MARK: - 通知名称
    static let activityProgressUpdatedNotification = Notification.Name("ActivityProgressUpdated")
    static let treasureBoxCountdownUpdatedNotification = Notification.Name("TreasureBoxCountdownUpdated")
    static let treasureBoxCountdownFinishedNotification = Notification.Name("TreasureBoxCountdownFinished")
    
    // MARK: - 公开方法
    
    /// 初始化并开始跟踪活跃度（只有第一次进入任务中心时调用）
    func initializeAndStartTracking() {
        guard !hasInitialized else {
            print("[ActivityProgress] 已经初始化过，跳过重复初始化")
            return
        }

        hasInitialized = true
        startTracking()
        print("[ActivityProgress] 首次初始化并开始跟踪活跃度")
    }

    /// 开始宝箱倒计时（全局唯一）
    /// - Parameter targetDuration: 目标倒计时时长（秒）
    func startTreasureBoxCountdown(targetDuration: Int) {
        // 停止之前的倒计时
        stopTreasureBoxCountdown()

        // 开始新的倒计时
        treasureBoxStartTime = Date()
        treasureBoxTargetDuration = targetDuration
        isTreasureBoxCountingDown = true

        // 启动定期检查定时器（每10秒检查一次倒计时状态）
        // 注意：这里只检查倒计时状态，不额外上报活跃度
        treasureBoxReportTimer = Timer.scheduledTimer(withTimeInterval: treasureBoxReportInterval, repeats: true) { [weak self] _ in
            self?.handleTreasureBoxReportTick()
        }

        print("[TreasureBox] 开始宝箱倒计时: \(targetDuration)秒")
        notifyTreasureBoxCountdownUpdated()
    }

    /// 停止宝箱倒计时
    func stopTreasureBoxCountdown() {
        guard isTreasureBoxCountingDown else { return }

        isTreasureBoxCountingDown = false
        treasureBoxStartTime = nil
        treasureBoxTargetDuration = 0
        treasureBoxReportTimer?.invalidate()
        treasureBoxReportTimer = nil

        print("[TreasureBox] 停止宝箱倒计时")
    }

    /// 获取宝箱倒计时剩余时间（秒）
    func getTreasureBoxRemainingSeconds() -> Int {
        guard isTreasureBoxCountingDown, let startTime = treasureBoxStartTime else { return 0 }

        let elapsed = Int(Date().timeIntervalSince(startTime))
        let remaining = max(0, treasureBoxTargetDuration - elapsed)
        return remaining
    }

    /// 检查宝箱倒计时是否已完成
    func isTreasureBoxCountdownFinished() -> Bool {
        return isTreasureBoxCountingDown && getTreasureBoxRemainingSeconds() <= 0
    }

    /// 获取宝箱倒计时状态
    func getTreasureBoxCountdownStatus() -> (isRunning: Bool, remainingSeconds: Int, targetDuration: Int) {
        return (
            isRunning: isTreasureBoxCountingDown,
            remainingSeconds: getTreasureBoxRemainingSeconds(),
            targetDuration: treasureBoxTargetDuration
        )
    }

    /// 开始跟踪活跃度（内部方法）
    private func startTracking() {
        guard !isTracking else { return }

        isTracking = true
        activityStartTime = Date()
        lastReportTime = Date()
        accumulatedSeconds = 0

        // 启动定时器，每30秒上报一次
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }

        print("[ActivityProgress] 开始跟踪活跃度")
    }
    
    /// 暂停跟踪活跃度（应用进入后台时）
    func pauseTracking() {
        guard isTracking else { return }

        // 停止定时器但保持跟踪状态
        reportTimer?.invalidate()
        reportTimer = nil

        // 上报剩余的活跃时长
        if accumulatedSeconds > 0 {
            reportAccumulatedProgress()
        }

        // 暂停宝箱倒计时定时器，但保持倒计时状态
        pauseTreasureBoxReportTimer()

        print("[ActivityProgress] 暂停跟踪活跃度")
    }

    /// 恢复跟踪活跃度（应用进入前台时）
    func resumeTracking() {
        guard hasInitialized && (reportTimer?.isValid != true) else { return }

        // 重新启动定时器
        lastReportTime = Date()
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }

        // 恢复宝箱倒计时定时器
        resumeTreasureBoxReportTimer()

        print("[ActivityProgress] 恢复跟踪活跃度")
    }

    /// 完全停止跟踪活跃度（仅用于清理）
    private func stopTracking() {
        isTracking = false
        reportTimer?.invalidate()
        reportTimer = nil
        activityStartTime = nil

        // 同时停止宝箱倒计时
        stopTreasureBoxCountdown()

        print("[ActivityProgress] 完全停止跟踪活跃度")
    }
    
    /// 手动上报活跃度（用于特殊场景，如首次激活）
    /// - Parameter seconds: 要上报的秒数
    func reportProgress(seconds: Int) {
        APIManager.shared.reportActiveProgress(activeSeconds: seconds) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.currentActiveData = data
                        self?.notifyProgressUpdated()
                        print("[ActivityProgress] 手动上报成功: \(seconds)秒")
                    } else {
                        print("[ActivityProgress] 手动上报失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 手动上报请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 获取当前活跃度数据
    func getCurrentActiveData() -> ActiveProgressData? {
        return currentActiveData
    }

    /// 更新当前活跃度数据（用于领取奖励后同步状态）
    func updateCurrentActiveData(_ data: ActiveProgressData) {
        currentActiveData = data
        // 更新基础活跃时长
        baseActiveSeconds = data.totalSeconds
        // 重置活跃度开始时间
        activityStartTime = Date()
        notifyProgressUpdated()
        print("[ActivityProgress] 活跃度数据已手动更新，基础时长: \(baseActiveSeconds)秒")
    }

    /// 获取当前实时总活跃时长（基础时长 + 本次会话时长）
    func getCurrentTotalActiveSeconds() -> Int {
        let sessionSeconds = getCurrentSessionActiveSeconds()
        let totalSeconds = baseActiveSeconds + sessionSeconds
        return totalSeconds
    }

    /// 获取当前会话的活跃时长
    private func getCurrentSessionActiveSeconds() -> Int {
        guard let startTime = activityStartTime else { return 0 }
        let sessionDuration = Date().timeIntervalSince(startTime)
        return Int(sessionDuration)
    }
    
    /// 刷新活跃度数据
    func refreshActiveData() {
        APIManager.shared.queryActiveProgress { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.currentActiveData = data
                        // 更新基础活跃时长
                        self?.baseActiveSeconds = data.totalSeconds
                        // 只有在首次初始化时才设置活跃度开始时间，避免重复刷新导致时间跳跃
                        if self?.activityStartTime == nil {
                            self?.activityStartTime = Date()
                            print("[ActivityProgress] 首次设置活跃度开始时间")
                        }
                        self?.notifyProgressUpdated()
                        print("[ActivityProgress] 刷新活跃度数据成功，基础时长: \(data.totalSeconds)秒")
                    } else {
                        print("[ActivityProgress] 刷新活跃度数据失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 刷新活跃度数据请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置通知监听
    private func setupNotifications() {
        // 监听应用进入前台和后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
    }
    
    /// 上报累计的活跃进度
    private func reportAccumulatedProgress() {
        guard isTracking else { return }
        
        // 计算自上次上报以来的活跃时长
        let now = Date()
        if let lastTime = lastReportTime {
            let interval = now.timeIntervalSince(lastTime)
            accumulatedSeconds += Int(interval)
        }
        
        // 如果累计时长大于0，则上报
        if accumulatedSeconds > 0 {
            let secondsToReport = accumulatedSeconds
            accumulatedSeconds = 0 // 重置累计时长
            
            APIManager.shared.reportActiveProgress(activeSeconds: secondsToReport) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess, let data = response.data {
                            self?.currentActiveData = data
                            self?.notifyProgressUpdated()
                            print("[ActivityProgress] 自动上报成功: \(secondsToReport)秒")
                        } else {
                            print("[ActivityProgress] 自动上报失败: \(response.displayMessage)")
                        }
                    case .failure(let error):
                        print("[ActivityProgress] 自动上报请求失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        lastReportTime = now
    }
    
    /// 发送活跃度更新通知
    private func notifyProgressUpdated() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: self,
            userInfo: ["data": currentActiveData as Any]
        )
    }

    // MARK: - 宝箱倒计时私有方法

    /// 暂停宝箱倒计时定时器
    private func pauseTreasureBoxReportTimer() {
        treasureBoxReportTimer?.invalidate()
        treasureBoxReportTimer = nil
        print("[TreasureBox] 暂停宝箱倒计时定时器")
    }

    /// 恢复宝箱倒计时定时器
    private func resumeTreasureBoxReportTimer() {
        guard isTreasureBoxCountingDown && treasureBoxReportTimer?.isValid != true else { return }

        treasureBoxReportTimer = Timer.scheduledTimer(withTimeInterval: treasureBoxReportInterval, repeats: true) { [weak self] _ in
            self?.handleTreasureBoxReportTick()
        }
        print("[TreasureBox] 恢复宝箱倒计时定时器")
    }

    /// 处理宝箱倒计时定期检查
    private func handleTreasureBoxReportTick() {
        guard isTreasureBoxCountingDown else { return }

        let remainingSeconds = getTreasureBoxRemainingSeconds()

        // 检查倒计时是否已完成
        if remainingSeconds <= 0 {
            handleTreasureBoxCountdownFinished()
            return
        }

        // 只发送倒计时更新通知，不进行额外的活跃度上报
        // 活跃度上报完全由现有的30秒定时器处理
        print("[TreasureBox] 宝箱倒计时检查: 剩余 \(remainingSeconds)秒")
        notifyTreasureBoxCountdownUpdated()
    }

    /// 处理宝箱倒计时完成
    private func handleTreasureBoxCountdownFinished() {
        print("[TreasureBox] 宝箱倒计时完成！")

        // 停止倒计时
        stopTreasureBoxCountdown()

        // 倒计时完成时不额外上报，让正常的活跃度上报机制处理
        // 这样可以避免重复上报导致的进度过快问题
        print("[TreasureBox] 倒计时完成，等待正常活跃度上报机制处理")

        // 发送倒计时完成通知
        notifyTreasureBoxCountdownFinished()
    }

    /// 发送宝箱倒计时更新通知
    private func notifyTreasureBoxCountdownUpdated() {
        let status = getTreasureBoxCountdownStatus()
        NotificationCenter.default.post(
            name: ActivityProgressManager.treasureBoxCountdownUpdatedNotification,
            object: self,
            userInfo: [
                "isRunning": status.isRunning,
                "remainingSeconds": status.remainingSeconds,
                "targetDuration": status.targetDuration
            ]
        )
    }

    /// 发送宝箱倒计时完成通知
    private func notifyTreasureBoxCountdownFinished() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.treasureBoxCountdownFinishedNotification,
            object: self,
            userInfo: ["targetDuration": treasureBoxTargetDuration]
        )
    }
    
    // MARK: - 通知处理
    
    @objc private func appDidBecomeActive() {
        // 应用进入前台时恢复跟踪（如果已经初始化过）
        resumeTracking()
    }

    @objc private func appWillResignActive() {
        // 应用进入后台时暂停跟踪
        pauseTracking()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        reportTimer?.invalidate()
        treasureBoxReportTimer?.invalidate()
    }
}

// MARK: - 扩展：便捷方法

extension ActivityProgressManager {
    
    /// 检查是否有可领取的奖励
    func hasAvailableRewards() -> Bool {
        guard let data = currentActiveData else { return false }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 检查当前未领取的奖励（当前宝箱）
        if let unclaimedReward = data.unclaimedActiveRewards {
            // 检查是否满足领取条件
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return remainingSeconds <= 0
        }

        return false
    }
    
    /// 获取当前宝箱的剩余时间（秒）- 使用实时计算
    func getCurrentRewardRemainingSeconds() -> Int? {
        guard let data = currentActiveData else { return nil }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 优先检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        // 如果没有当前宝箱，检查下一个奖励
        if let nextReward = data.nextShortVideoRewardConfig {
            let remainingSeconds = nextReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        return nil
    }
    
    /// 获取当前可领取的奖励金币数
    func getCurrentAvailableReward() -> Int {
        guard let data = currentActiveData else { return 0 }

        // 检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - data.totalSeconds
            if remainingSeconds <= 0 {
                return unclaimedReward.rewardValue
            }
        }

        return 0
    }

    /// 获取当前宝箱的奖励配置（无论是否可领取）
    func getCurrentRewardConfig() -> ActiveRewardConfig? {
        guard let data = currentActiveData else { return nil }

        // 优先返回当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            return unclaimedReward
        }

        // 如果没有当前宝箱，返回下一个奖励配置
        return data.nextShortVideoRewardConfig
    }

    /// 开始当前宝箱的倒计时（基于当前宝箱配置）
    func startCurrentTreasureBoxCountdown() {
        guard let currentReward = getCurrentRewardConfig() else {
            print("[TreasureBox] 无法开始倒计时：没有当前宝箱配置")
            return
        }

        // 计算剩余时间作为倒计时目标
        let remainingSeconds = getCurrentRewardRemainingSeconds() ?? 0

        if remainingSeconds > 0 {
            startTreasureBoxCountdown(targetDuration: remainingSeconds)
            print("[TreasureBox] 开始当前宝箱倒计时: \(remainingSeconds)秒, 奖励: \(currentReward.rewardValue)金币")
        } else {
            print("[TreasureBox] 当前宝箱已可领取，无需倒计时")
        }
    }

    /// 检查并自动开始下一个宝箱倒计时（在领取奖励后调用）
    func checkAndStartNextTreasureBoxCountdown() {
        // 刷新活跃度数据以获取最新的宝箱配置
        refreshActiveData()

        // 延迟一点时间等待数据刷新完成，然后开始下一个宝箱倒计时
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.startCurrentTreasureBoxCountdown()
        }
    }

    /// 获取活跃度调试信息（用于调试）
    func getActivityDebugInfo() -> (baseSeconds: Int, sessionSeconds: Int, totalSeconds: Int, startTime: Date?) {
        let sessionSeconds = getCurrentSessionActiveSeconds()
        let totalSeconds = baseActiveSeconds + sessionSeconds
        return (
            baseSeconds: baseActiveSeconds,
            sessionSeconds: sessionSeconds,
            totalSeconds: totalSeconds,
            startTime: activityStartTime
        )
    }
}
